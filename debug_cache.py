#!/usr/bin/env python3
"""
Debug script to check cache functionality directly
"""

import sys
import os
import json

# Add ShopeeAPI to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'ShopeeAPI'))

def test_cache_directly():
    """Test the cache functionality directly"""
    print("DIRECT CACHE TEST")
    print("=" * 50)
    
    try:
        from client import ShopeeAPI
        
        # Initialize API
        print("Initializing ShopeeAPI...")
        api = ShopeeAPI()
        
        # Get chat service
        chat_service = api.chat_service
        
        print(f"Cache enabled: {chat_service.config.cache['enabled']}")
        print(f"Username cache enabled: {chat_service.config.cache['username_to_conversation_id']['enabled']}")
        print(f"Cache expiry: {chat_service.config.cache['username_to_conversation_id']['expiry_seconds']}")
        
        # Test cache operations
        username = "me0tn_14qo"
        
        print(f"\n1. Testing cache get for '{username}'...")
        cached = chat_service.cache_manager.username_to_conversation_id.get(username.lower())
        print(f"   Cached result: {cached}")
        
        print(f"\n2. Testing cache set for '{username}'...")
        test_data = {
            "id": "test_conversation_id",
            "to_id": 12345,
            "to_name": username
        }
        chat_service.cache_manager.username_to_conversation_id.set(username.lower(), test_data)
        print("   Cache set completed")
        
        print(f"\n3. Testing cache get again for '{username}'...")
        cached = chat_service.cache_manager.username_to_conversation_id.get(username.lower())
        print(f"   Cached result: {cached}")
        
        print(f"\n4. Checking cache file...")
        cache_file = "ShopeeAPI/cache/username_conversation_cache.json"
        if os.path.exists(cache_file):
            with open(cache_file, 'r') as f:
                cache_data = json.load(f)
            print(f"   Cache file exists with {len(cache_data.get('entries', {}))} entries")
            print(f"   Expiry in file: {cache_data.get('expiry_seconds')}")
            if username.lower() in cache_data.get('entries', {}):
                print(f"   ✅ Username '{username}' found in cache file")
            else:
                print(f"   ❌ Username '{username}' NOT found in cache file")
        else:
            print("   ❌ Cache file does not exist")
            
        print(f"\n5. Testing get_conversation_info_by_username method...")
        conversation_info, error = chat_service.get_conversation_info_by_username(username)
        if error:
            print(f"   ❌ Error: {error}")
        else:
            print(f"   ✅ Success: {conversation_info.get('id') if conversation_info else 'None'}")
            
        print(f"\n6. Checking cache again after method call...")
        cached = chat_service.cache_manager.username_to_conversation_id.get(username.lower())
        print(f"   Cached result: {cached is not None}")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_cache_directly()
