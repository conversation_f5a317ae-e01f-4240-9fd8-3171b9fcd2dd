#!/usr/bin/env python3
"""
<PERSON><PERSON>t to add timing instrumentation to ShopeeAPI chat service
This will show exactly where time is spent in the send_chat_message process
"""

import os
import shutil
from datetime import datetime

def backup_original_file():
    """Create a backup of the original chat.py file"""
    original_file = "ShopeeAPI/services/chat.py"
    backup_file = f"ShopeeAPI/services/chat.py.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    
    if os.path.exists(original_file):
        shutil.copy2(original_file, backup_file)
        print(f"✅ Backup created: {backup_file}")
        return True
    else:
        print(f"❌ Original file not found: {original_file}")
        return False

def add_timing_imports():
    """Add timing imports to the chat.py file"""
    original_file = "ShopeeAPI/services/chat.py"
    
    # Read the original file
    with open(original_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Add timing import after the existing imports
    import_addition = """import time
import logging

# Timing instrumentation
timing_logger = logging.getLogger('timing')
timing_logger.setLevel(logging.INFO)
if not timing_logger.handlers:
    handler = logging.StreamHandler()
    formatter = logging.Formatter('TIMING: %(message)s')
    handler.setFormatter(formatter)
    timing_logger.addHandler(handler)

"""
    
    # Find where to insert the timing imports (after the existing imports)
    lines = content.split('\n')
    insert_index = 0
    
    # Find the end of imports (look for the first class or function definition)
    for i, line in enumerate(lines):
        if line.strip().startswith('class ') or line.strip().startswith('def '):
            insert_index = i
            break
    
    # Insert the timing imports
    lines.insert(insert_index, import_addition)
    
    # Write back to file
    with open(original_file, 'w', encoding='utf-8') as f:
        f.write('\n'.join(lines))
    
    print("✅ Added timing imports")

def instrument_send_chat_message():
    """Add timing instrumentation to send_chat_message method"""
    original_file = "ShopeeAPI/services/chat.py"
    
    # Read the file
    with open(original_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Find the send_chat_message method and add timing
    lines = content.split('\n')
    new_lines = []
    in_send_chat_message = False
    method_indent = 0
    
    for i, line in enumerate(lines):
        if 'def send_chat_message(self, payload:' in line:
            in_send_chat_message = True
            method_indent = len(line) - len(line.lstrip())
            new_lines.append(line)
            # Add timing start
            new_lines.append(' ' * (method_indent + 8) + 'timing_logger.info("=== SEND_CHAT_MESSAGE START ===")')
            new_lines.append(' ' * (method_indent + 8) + 'overall_start = time.monotonic()')
            continue
        
        if in_send_chat_message:
            # Check if we're at the end of the method
            if line.strip() and len(line) - len(line.lstrip()) <= method_indent and not line.strip().startswith('#'):
                if line.strip().startswith('def ') or line.strip().startswith('class '):
                    in_send_chat_message = False
            
            # Add timing for conversation lookup
            if '_get_conversation_info_from_search(username)' in line:
                new_lines.append(' ' * (method_indent + 12) + 'timing_logger.info("Step 1: Starting conversation lookup...")')
                new_lines.append(' ' * (method_indent + 12) + 'lookup_start = time.monotonic()')
                new_lines.append(line)
                new_lines.append(' ' * (method_indent + 12) + 'lookup_time = time.monotonic() - lookup_start')
                new_lines.append(' ' * (method_indent + 12) + 'timing_logger.info(f"Step 1: Conversation lookup took {lookup_time:.3f}s")')
                continue
            
            # Add timing for message splitting
            if 'message_parts = self._split_message(text)' in line:
                new_lines.append(' ' * (method_indent + 12) + 'timing_logger.info("Step 2: Starting message split...")')
                new_lines.append(' ' * (method_indent + 12) + 'split_start = time.monotonic()')
                new_lines.append(line)
                new_lines.append(' ' * (method_indent + 12) + 'split_time = time.monotonic() - split_start')
                new_lines.append(' ' * (method_indent + 12) + 'timing_logger.info(f"Step 2: Message split took {split_time:.3f}s, created {len(message_parts)} parts")')
                continue
            
            # Add timing for each message POST
            if 'chat_response = self.session.post(' in line and 'self.config.urls["chat_message"]' in lines[i+1] if i+1 < len(lines) else False:
                new_lines.append(' ' * (method_indent + 16) + 'timing_logger.info(f"Step 3.{i+1}: Sending message part {i+1} to Shopee...")')
                new_lines.append(' ' * (method_indent + 16) + 'post_start = time.monotonic()')
                new_lines.append(line)
                new_lines.append(lines[i+1])  # The URL line
                new_lines.append(' ' * (method_indent + 16) + 'post_time = time.monotonic() - post_start')
                new_lines.append(' ' * (method_indent + 16) + 'timing_logger.info(f"Step 3.{i+1}: Message part {i+1} took {post_time:.3f}s")')
                # Skip the next line since we already added it
                continue
            
            # Add timing at the end of successful execution
            if 'return final_response, 200' in line:
                new_lines.append(' ' * (method_indent + 12) + 'overall_time = time.monotonic() - overall_start')
                new_lines.append(' ' * (method_indent + 12) + 'timing_logger.info(f"=== SEND_CHAT_MESSAGE COMPLETE: {overall_time:.3f}s total ===")')
                new_lines.append(line)
                continue
        
        new_lines.append(line)
    
    # Write back to file
    with open(original_file, 'w', encoding='utf-8') as f:
        f.write('\n'.join(new_lines))
    
    print("✅ Added timing instrumentation to send_chat_message")

def instrument_conversation_lookup():
    """Add timing to conversation lookup methods"""
    original_file = "ShopeeAPI/services/chat.py"
    
    # Read the file
    with open(original_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Add timing to _get_conversation_info_from_search
    content = content.replace(
        'def _get_conversation_info_from_search(self, username: str)',
        '''def _get_conversation_info_from_search(self, username: str)'''
    )
    
    # Add timing around the search API call
    content = content.replace(
        'conversation_response = self.session.get(\n                self.config.urls["conversation_search"],',
        '''timing_logger.info("  → Calling combined_search API...")
            search_start = time.monotonic()
            conversation_response = self.session.get(
                self.config.urls["conversation_search"],'''
    )
    
    content = content.replace(
        'if conversation_response.status_code != 200:',
        '''search_time = time.monotonic() - search_start
            timing_logger.info(f"  → Combined search API took {search_time:.3f}s")
            
            if conversation_response.status_code != 200:'''
    )
    
    # Write back to file
    with open(original_file, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("✅ Added timing to conversation lookup methods")

def main():
    """Main function to add all timing instrumentation"""
    print("Adding timing instrumentation to ShopeeAPI chat service...")
    print("=" * 60)
    
    # Step 1: Backup original file
    if not backup_original_file():
        return
    
    # Step 2: Add timing imports
    add_timing_imports()
    
    # Step 3: Instrument send_chat_message
    instrument_send_chat_message()
    
    # Step 4: Instrument conversation lookup
    instrument_conversation_lookup()
    
    print("=" * 60)
    print("✅ Timing instrumentation added successfully!")
    print()
    print("Now run the test again to see detailed timing breakdown:")
    print("python test_chat_timing.py")
    print()
    print("To restore the original file later:")
    print("cp ShopeeAPI/services/chat.py.backup_* ShopeeAPI/services/chat.py")

if __name__ == "__main__":
    main()
