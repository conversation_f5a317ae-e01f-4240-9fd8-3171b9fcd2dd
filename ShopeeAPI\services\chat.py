"""
Services for managing Shopee chat functionality.
"""
from typing import Dict, Any, List, Tuple, Optional
import uuid
import time
import os
import json
import logging
from urllib.parse import quote

# Configure logging
logger = logging.getLogger(__name__)

# Try different import approaches to handle both package and direct imports
try:
    from core.session import ShopeeSession
    from core.config import ShopeeConfig
    from core.cache import CacheManager
    from core.exceptions import RequestError, ResourceNotFoundError
    from core.image_cache import ImageCache
    from utils.helpers import generate_request_id, get_image_dimensions, calculate_thumbnail_dimensions
except ImportError as e:
    print(f"Import error in chat.py: {e}")
    # Try adding current directory to path and importing again
    try:
        import sys
        import os
        current_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        if current_dir not in sys.path:
            sys.path.insert(0, current_dir)
        from core.session import ShopeeSession
        from core.config import ShopeeConfig
        from core.cache import Cache<PERSON>anager
        from core.exceptions import RequestError, ResourceNotFoundError
        from utils.helpers import generate_request_id, get_image_dimensions, calculate_thumbnail_dimensions
    except ImportError as e2:
        print(f"Path-based import also failed in chat.py: {e2}")
        # Last resort: try relative imports (only works when run as package)
        try:
            from ..core.session import ShopeeSession
            from ..core.config import ShopeeConfig
            from ..core.cache import CacheManager
            from ..core.exceptions import RequestError, ResourceNotFoundError
            from ..utils.helpers import generate_request_id, get_image_dimensions, calculate_thumbnail_dimensions
        except ImportError as e3:
            print(f"Relative import also failed in chat.py: {e3}")
            raise ImportError(f"Could not import required modules in chat.py: {e}")


import time
import logging

# Timing instrumentation
timing_logger = logging.getLogger('timing')
timing_logger.setLevel(logging.INFO)
if not timing_logger.handlers:
    handler = logging.StreamHandler()
    formatter = logging.Formatter('TIMING: %(message)s')
    handler.setFormatter(formatter)
    timing_logger.addHandler(handler)


class ChatService:
    """
    Service for interacting with Shopee chat APIs.
    """

    def __init__(self, session: ShopeeSession, config: ShopeeConfig):
        """
        Initialize with session and config.

        Args:
            session: ShopeeSession instance
            config: ShopeeConfig instance
        """
        self.session = session
        self.config = config

        # Initialize cache manager
        cache_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'cache')
        # Create cache directory if it doesn't exist
        if not os.path.exists(cache_dir):
            os.makedirs(cache_dir)
            
        # Initialize image cache with configurable settings
        # Get image cache settings from config (with defaults)
        image_cache_config = getattr(config, 'image_cache', {})
        if not image_cache_config and hasattr(config, 'raw_config'):
            # Try to get from raw config if not available as attribute
            image_cache_config = config.raw_config.get('IMAGE_CACHE', {})
        
        max_age_hours = image_cache_config.get('MAX_AGE_HOURS', image_cache_config.get('max_age_hours', 24))
        max_cache_size_mb = image_cache_config.get('MAX_CACHE_SIZE_MB', image_cache_config.get('max_cache_size_mb', 100))
        
        self.image_cache = ImageCache(
            cache_dir=cache_dir,
            max_age_hours=max_age_hours,
            max_cache_size_mb=max_cache_size_mb
        )
        self.cache_manager = CacheManager(self.config.cache, cache_dir)

    def _get_common_params(self) -> Dict[str, str]:
        """
        Get common parameters for chat API requests.

        Returns:
            Dictionary of common parameters
        """
        return {
            "_uid": f"0-{self.config.shop_id}",
            "_v": "8.8.9",  # Updated from 8.5.6 to match browser request
            "csrf_token": self.session.credential_manager.get_csrf_token(),
            "SPC_CDS_CHAT": self.session.credential_manager.get_spc_cds_chat(),
            "x-shop-region": self.config.region_id,
            "_api_source": "webchat"  # Add missing _api_source parameter
        }

    def _get_conversation_info_from_search(self, username: str) -> Tuple[Dict[str, Any], Optional[Dict[str, Any]]]:
        """
        Get conversation information using the search endpoint.
        This method uses the combined search API to get conversation details directly.

        Args:
            username: Username to search for

        Returns:
            Tuple with conversation info and error (if any)
        """
        if not username:
            return None, {"error": "Username is required"}

        logger.debug(f"Getting conversation info from search endpoint for username: {username}")

        # Get CSRF token and SPC_CDS_CHAT token
        csrf_token = self.session.credential_manager.get_csrf_token()
        spc_cds_chat = self.session.credential_manager.get_spc_cds_chat()

        if not csrf_token:
            logger.warning("CSRF token not found")
            csrf_token = ""

        if not spc_cds_chat:
            logger.warning("SPC_CDS_CHAT token not found")
            spc_cds_chat = ""

        # Prepare the params for the conversation search request using the combined search API
        # Match the exact parameters from the working request
        params = {
            "per_page": 20,
            "keyword": username,
            "type": 3,
            "biz_id": 0,
            "_uid": f"0-{self.config.shop_id}",
            "_v": "8.8.9",
            "csrf_token": csrf_token,
            "SPC_CDS_CHAT": spc_cds_chat,
            "x-shop-region": self.config.region_id,
            "_api_source": "webchat"
        }

        try:
            # Send the GET request to search for conversation info

            timing_logger.info("  → Calling combined_search API...")
            search_start = time.monotonic()
            conversation_response = self.session.get(
                self.config.urls["conversation_search"],
                params=params
            )

            search_time = time.monotonic() - search_start
            timing_logger.info(f"  → Combined search API took {search_time:.3f}s")
            
            if conversation_response.status_code != 200:
                return None, {"error": f"Failed to retrieve conversation info: HTTP {conversation_response.status_code}"}

            # Extract conversation info from the search results
            search_results = conversation_response.json()

            # First check the conversation_search_result for direct conversation matches
            if 'conversation_search_result' in search_results and 'conversations' in search_results['conversation_search_result']:
                conversations = search_results['conversation_search_result']['conversations']

                # Look for a conversation with the matching username
                for conversation in conversations:
                    # Check both to_name and buyer_name fields for the username
                    to_name = conversation.get('to_name', '').lower()
                    buyer_name = conversation.get('buyer_name', '').lower()

                    if to_name == username.lower() or buyer_name == username.lower():
                        # Found the conversation! Look for conversation_id in message search results first
                        buyer_id = conversation.get('buyer_id')
                        buyer_name = conversation.get('buyer_name')

                        # Always use the API call to get the correct conversation details
                        full_conversation_info, error = self._get_conversation_by_user_id(buyer_id)
                        if full_conversation_info and not error:
                            # Update the conversation info with buyer details from search results
                            full_conversation_info["buyer_id"] = buyer_id
                            full_conversation_info["buyer_name"] = buyer_name
                            full_conversation_info["buyer_avatar"] = conversation.get('buyer_avatar')
                            full_conversation_info["buyer_avatar_hash"] = conversation.get('buyer_avatar_hash')
                            return full_conversation_info, None
                        else:
                            # Fallback: create basic conversation info from search results
                            conversation_info = {
                                "id": None,
                                "conversation_id": None,
                                "to_id": buyer_id,
                                "to_name": buyer_name,
                                "to_avatar": conversation.get('buyer_avatar'),
                                "to_avatar_hash": conversation.get('buyer_avatar_hash'),
                                "shop_id": conversation.get('shop_id'),
                                "shop_name": conversation.get('shop_name'),
                                "shop_region": conversation.get('shop_region'),
                                "buyer_id": buyer_id,
                                "buyer_name": buyer_name
                            }
                            return conversation_info, None

            # If not found in conversation results, check the message_search_result
            if 'message_search_result' in search_results and 'messages' in search_results['message_search_result']:
                messages = search_results['message_search_result']['messages']

                # Look for a message with the matching username
                for message in messages:
                    # Check the to_name field for the username
                    to_name = message.get('to_name', '').lower()

                    if to_name == username.lower():
                        # Found the conversation! Extract the needed information from message data
                        conversation_info = {
                            "id": message.get('conversation_id'),
                            "conversation_id": message.get('conversation_id'),
                            "to_id": message.get('to_id'),
                            "to_name": message.get('to_name'),
                            "to_avatar": message.get('to_avatar'),
                            "to_avatar_hash": message.get('to_avatar_hash'),
                            "shop_id": message.get('shop_id'),
                            "shop_name": message.get('shop_name'),
                            "shop_region": message.get('shop_region'),
                            "buyer_id": message.get('to_id'),  # Use to_id as buyer_id for compatibility
                            "buyer_name": message.get('to_name')
                        }

                        return conversation_info, None

            # If not found in conversation results, check order search results
            if 'order_search_result' in search_results and 'orders' in search_results['order_search_result']:
                orders = search_results['order_search_result']['orders']

                for order in orders:
                    # Check if this order has buyer information matching the username
                    if order.get('buyer_name', '').lower() == username.lower():
                        # Create conversation info from order data
                        # Note: We don't have conversation_id from order results, so we'll need to make another call
                        buyer_id = order.get('buyer_id')
                        if buyer_id:
                            # Try to get conversation info using the user_id
                            return self._get_conversation_by_user_id(buyer_id)

            
            # Fallback: Search in to_ship orders for new users who haven't chatted before
            logger.info(f"User '{username}' not found in combined search, trying order-based fallback")
            return self._get_conversation_info_from_orders(username)

        except Exception as e:
            return None, {"error": f"Exception getting conversation info: {str(e)}"}

    def search_conversation_raw(self, username: str) -> Tuple[Dict[str, Any], Optional[Dict[str, Any]]]:
        """
        Search for conversations using the combined search API and return raw results.
        This method returns the complete search response including conversation_search_result,
        order_search_result, and agent_msg_search_result.

        Args:
            username: Username to search for

        Returns:
            Tuple with raw search results and error (if any)
        """
        if not username:
            return None, {"error": "Username is required"}

        logger.debug(f"Searching conversations for username: {username}")

        # Get CSRF token and SPC_CDS_CHAT token
        csrf_token = self.session.credential_manager.get_csrf_token()
        spc_cds_chat = self.session.credential_manager.get_spc_cds_chat()

        if not csrf_token:
            logger.warning("CSRF token not found")
            csrf_token = ""

        if not spc_cds_chat:
            logger.warning("SPC_CDS_CHAT token not found")
            spc_cds_chat = ""

        # Prepare the params for the conversation search request using the combined search API
        # Match the exact parameters from the working request
        params = {
            "per_page": 20,
            "keyword": username,
            "type": 3,
            "biz_id": 0,
            "_uid": f"0-{self.config.shop_id}",
            "_v": "8.8.9",
            "csrf_token": csrf_token,
            "SPC_CDS_CHAT": spc_cds_chat,
            "x-shop-region": self.config.region_id,
            "_api_source": "webchat"
        }

        try:
            # Send the GET request to search for conversation info
            logger.debug(f"Sending search request to: {self.config.urls['conversation_search']}")
            logger.debug(f"Search params: {params}")

            timing_logger.info("  → Calling combined_search API...")
            search_start = time.monotonic()
            conversation_response = self.session.get(
                self.config.urls["conversation_search"],
                params=params
            )

            search_time = time.monotonic() - search_start
            timing_logger.info(f"  → Combined search API took {search_time:.3f}s")
            
            if conversation_response.status_code != 200:
                logger.error(f"Search API returned status code {conversation_response.status_code}")
                logger.debug(f"Error response: {conversation_response.text}")
                return None, {"error": f"Failed to retrieve conversation info: HTTP {conversation_response.status_code}"}

            # Return the raw search results
            search_results = conversation_response.json()
            logger.debug(f"Search results found: {len(search_results.get('conversation_search_result', {}).get('conversations', []))} conversations, {len(search_results.get('order_search_result', {}).get('orders', []))} orders")

            return search_results, None

        except Exception as e:
            logger.error(f"Exception in search_conversation_raw: {str(e)}")
            return None, {"error": f"Exception searching conversations: {str(e)}"}

    def _split_message(self, text: str, max_length: int = 600) -> List[str]:
        """
        Split a long message into multiple parts if it exceeds the maximum length.

        Args:
            text: The message text to split
            max_length: Maximum length per message (default: 600 characters)

        Returns:
            List of message parts
        """
        if len(text) <= max_length:
            return [text]

        parts = []
        remaining_text = text

        while remaining_text:
            if len(remaining_text) <= max_length:
                parts.append(remaining_text)
                break

            # Find the best split point (prefer splitting at word boundaries)
            split_point = max_length

            # Look for a space or punctuation near the end to split at word boundary
            for i in range(max_length - 50, max_length):
                if i < len(remaining_text) and remaining_text[i] in ' .,!?;:\n':
                    split_point = i + 1
                    break

            # If no good split point found, just split at max_length
            if split_point == max_length and len(remaining_text) > max_length:
                split_point = max_length

            parts.append(remaining_text[:split_point].strip())
            remaining_text = remaining_text[split_point:].strip()

        return parts

    def send_chat_message(self, payload: Dict[str, Any]) -> Tuple[Dict[str, Any], int]:
            timing_logger.info("=== SEND_CHAT_MESSAGE START ===")
            overall_start = time.monotonic()
        """
        Send a text message to a user. Automatically splits messages longer than 500 characters.

        Args:
            payload: Dictionary containing message details
                - text: Message text
                - username: Username to send to
                - force_send_cancel_order_warning: Whether to force send despite cancel warning
                - comply_cancel_order_warning: Whether to comply with cancel warning

        Returns:
            Tuple with response data and HTTP status code
        """
        username = payload.get('username')
        text = payload.get('text')

        if not username:
            return {"error": "Username is required"}, 400

        if not text:
            return {"error": "Message text is required"}, 400

        try:
            # Split the message if it's too long
                timing_logger.info("Step 2: Starting message split...")
                split_start = time.monotonic()
            message_parts = self._split_message(text)
                split_time = time.monotonic() - split_start
                timing_logger.info(f"Step 2: Message split took {split_time:.3f}s, created {len(message_parts)} parts")

            # Get conversation info by username using the search endpoint
                timing_logger.info("Step 1: Starting conversation lookup...")
                lookup_start = time.monotonic()
            conversation_info, error = self._get_conversation_info_from_search(username)
                lookup_time = time.monotonic() - lookup_start
                timing_logger.info(f"Step 1: Conversation lookup took {lookup_time:.3f}s")
            if error:
                return error, 500

            # Extract conversation_id and to_id from the search response
            conversation_id = conversation_info.get('id')
            to_id = conversation_info.get('to_id')

            if not conversation_id or not to_id:
                return {"error": "Failed to get conversation info"}, 500

            # Send each message part
            responses = []
            for i, message_part in enumerate(message_parts):

                # Prepare message payload
                message_payload = {
                    "request_id": generate_request_id(),
                    "to_id": to_id,
                    "type": "text",
                    "content": {
                        "text": message_part,
                        "uid": str(uuid.uuid4())
                    },
                    "shop_id": self.config.shop_id,
                    "chat_send_option": {
                        "force_send_cancel_order_warning": payload.get('force_send_cancel_order_warning', False),
                        "comply_cancel_order_warning": payload.get('comply_cancel_order_warning', False)
                    },
                    "entry_point": "direct_chat_entry_point",
                    "choice_info": {
                        "real_shop_id": None
                    },
                    "conversation_id": conversation_id,
                    "re_policy": {
                        "dfp_access_f": "P6HFksuWQ8Tap+DnhVxOqA==|5hMi/K0kBKyUzxNNpYITKExyqO91xRwAnZhXKuh6M1gdq6FwNBmbnbrseJFi5oNJb5t6y18W0D0YIw==|u0PcFqU736y8ZD/U|08|3"
                    }
                }

                # Send the message (no query parameters needed - authentication handled via headers)
                    timing_logger.info(f"Step 3.{i+1}: Sending message part {i+1} to Shopee...")
                    post_start = time.monotonic()
                chat_response = self.session.post(
                    self.config.urls["chat_message"],
                    post_time = time.monotonic() - post_start
                    timing_logger.info(f"Step 3.{i+1}: Message part {i+1} took {post_time:.3f}s")
                    self.config.urls["chat_message"],
                    json=message_payload
                )

                if chat_response.status_code != 200:
                    return {"error": f"Failed to send chat message part {i+1}: {chat_response.text}"}, 500

                responses.append(chat_response.json())

                # Add a small delay between messages to avoid rate limiting
                if i < len(message_parts) - 1:  # Don't delay after the last message
                    import time
                    time.sleep(0.5)  # 500ms delay between messages

            # Return the response from the last message, but include info about multiple parts
            final_response = responses[-1] if responses else {}
            if len(message_parts) > 1:
                final_response["message_parts_sent"] = len(message_parts)

                overall_time = time.monotonic() - overall_start
                timing_logger.info(f"=== SEND_CHAT_MESSAGE COMPLETE: {overall_time:.3f}s total ===")
            return final_response, 200
        except Exception as e:
            return {"error": f"Exception in send_chat_message: {str(e)}"}, 500

    def send_image_message(self, payload: Dict[str, Any]) -> Tuple[Dict[str, Any], int]:
        """
        Send an image message to a user by uploading the image first, then sending the message.

        Args:
            payload: Dictionary containing message details
                - username: Username to send to
                - image_url: URL of the image to send

        Returns:
            Tuple with response data and HTTP status code
        """
        username = payload.get('username')
        image_url = payload.get('image_url')

        if not username:
            return {"error": "Username is required"}, 400

        if not image_url:
            return {"error": "Image URL is required"}, 400

        try:
            # Get conversation info by username using the search endpoint
            conversation_info, error = self._get_conversation_info_from_search(username)
            if error:
                return error, 500

            # Debug conversation info

            # Extract conversation_id and to_id from the search response
            conversation_id = conversation_info.get('id')
            to_id = conversation_info.get('to_id')

            if not conversation_id or not to_id:
                return {"error": "Failed to get conversation info"}, 500


            # Step 1: Upload the image to Shopee
            upload_result, upload_status = self._upload_image_to_shopee(image_url, conversation_id)
            if upload_status != 200:
                return upload_result, upload_status


            # Step 2: Send the image message using the uploaded image info
            return self._send_uploaded_image_message(
                conversation_id, 
                to_id, 
                upload_result, 
                payload
            )

        except Exception as e:
            return {"error": f"Exception in send_image_message: {str(e)}"}, 500

    def _upload_image_to_shopee(self, image_url: str, conversation_id: str) -> Tuple[Dict[str, Any], int]:
        """
        Upload an image to Shopee's image upload endpoint with caching.

        Args:
            image_url: URL of the image to download and upload
            conversation_id: Conversation ID for the upload

        Returns:
            Tuple with upload response data and HTTP status code
        """
        # Check cache first
        cached_result = self.image_cache.get_cached_upload(image_url, conversation_id)
        if cached_result:
            return cached_result, 200
        

        try:
            # Download the image
            import requests
            response = requests.get(image_url, timeout=30)
            if response.status_code != 200:
                return {"error": f"Failed to download image from URL: HTTP {response.status_code}"}, 400

            image_data = response.content
            content_type = response.headers.get('content-type', 'image/jpeg')
            
            # Determine file extension based on content type
            extension_map = {
                'image/jpeg': 'jpg',
                'image/jpg': 'jpg', 
                'image/png': 'png',
                'image/gif': 'gif',
                'image/webp': 'webp'
            }
            extension = extension_map.get(content_type, 'jpg')
            

            # Prepare the upload parameters (matching your specification)
            params = {
                "_uid": f"0-{self.config.shop_id}",
                "_v": "8.8.9",
                "csrf_token": self.session.credential_manager.get_csrf_token(),
                "SPC_CDS_CHAT": self.session.credential_manager.get_spc_cds_chat(),
                "x-shop-region": self.config.region_id,
                "_api_source": "webchat"
            }

            # Prepare the form data
            form_data = {
                'conversation_id': (None, str(conversation_id))
            }
            
            # Add the file data
            files = {
                'file': (f'image.{extension}', image_data, content_type)
            }


            # Upload the image
            upload_response = self.session.post(
                self.config.urls["chat_image_upload"],
                params=params,
                data=form_data,
                files=files
            )


            if upload_response.status_code != 200:
                return {"error": f"Failed to upload image: {upload_response.text}"}, upload_response.status_code

            upload_result = upload_response.json()
            
            # Cache the successful upload result
            self.image_cache.cache_upload_result(
                image_url=image_url,
                image_data=image_data,
                content_type=content_type,
                upload_result=upload_result,
                conversation_id=conversation_id
            )
            
            return upload_result, 200

        except requests.exceptions.RequestException as e:
            return {"error": f"Failed to download image: {str(e)}"}, 400
        except Exception as e:
            return {"error": f"Exception uploading image: {str(e)}"}, 500

    def _send_uploaded_image_message(self, conversation_id: str, to_id: str, upload_result: Dict[str, Any], payload: Dict[str, Any]) -> Tuple[Dict[str, Any], int]:
        """
        Send an image message using the result from an image upload.

        Args:
            conversation_id: Conversation ID
            to_id: Recipient user ID
            upload_result: Result from the image upload
            payload: Original payload with user preferences

        Returns:
            Tuple with response data and HTTP status code
        """

        try:
            # Extract image information from upload result
            # The structure depends on what Shopee returns, we'll handle common cases
            image_info = upload_result.get('data', upload_result)
            
            # Common fields that might be in the upload result
            image_url = image_info.get('url', image_info.get('image_url', ''))
            image_width = image_info.get('width', payload.get('width', 500))
            image_height = image_info.get('height', payload.get('height', 500))
            thumb_url = image_info.get('thumb_url', image_url)
            thumb_width = image_info.get('thumb_width', payload.get('thumb_width', 500))
            thumb_height = image_info.get('thumb_height', payload.get('thumb_height', 500))
            url_hash = image_info.get('url_hash', payload.get('url_hash', ''))
            file_server_id = image_info.get('file_server_id', payload.get('file_server_id', 0))

            # Generate UID using the same method as the example
            import random
            uid = str(random.randint(1000000000000000000, 9999999999999999999)) + str(random.randint(10000, 99999))

            # Prepare message payload to match Shopee's expected format
            message_payload = {
                "request_id": str(uuid.uuid4()),
                "to_id": int(to_id),
                "type": "image",
                "content": {
                    "uid": uid,
                    "width": image_width,
                    "height": image_height,
                    "url_hash": url_hash,
                    "url": image_url,
                    "thumb_url": thumb_url,
                    "thumb_width": thumb_width,
                    "thumb_height": thumb_height,
                    "file_server_id": file_server_id
                },
                "shop_id": int(self.config.shop_id),
                "chat_send_option": {
                    "force_send_cancel_order_warning": payload.get('force_send_cancel_order_warning', False),
                    "comply_cancel_order_warning": payload.get('comply_cancel_order_warning', False)
                },
                "entry_point": "direct_chat_entry_point",
                "choice_info": {
                    "real_shop_id": None
                },
                "conversation_id": str(conversation_id),
                "re_policy": {
                    "dfp_access_f": "uJ+EdxhMeLz1ZTcQSlzt/A==|aRci/K0kBKyUzxNNpYITKExyqO91xRwAnZhXKpIaePceq6FwNBmbnbrseJFi5oNJb5t6y18W0D0=|u0PcFqU736y8ZD/U|08|3"
                }
            }

            # Send the message (no query parameters needed - authentication handled via headers)

            chat_response = self.session.post(
                self.config.urls["chat_message"],
                json=message_payload
            )

            
            if chat_response.status_code != 200:
                return {"error": f"Failed to send image message: {chat_response.text}"}, chat_response.status_code

            return chat_response.json(), 200

        except Exception as e:
            return {"error": f"Exception sending uploaded image message: {str(e)}"}, 500

    def send_order_card_message(self, order_sn: str) -> Tuple[Dict[str, Any], int]:
        """
        Send an order card message to a user.

        Args:
            order_sn: Order number

        Returns:
            Tuple with response data and HTTP status code
        """
        logger.debug(f"Sending order card message for order: {order_sn}")

        try:
            # Import OrderService locally to avoid circular dependency
            from .orders import OrderService
            order_service = OrderService(self.session, self.config)

            # Get order details to extract buyer_user_id and order_id
            order_details, status_code = order_service.get_order_details_by_sn(order_sn)
            if status_code != 200:
                logger.error(f"Failed to get order details for {order_sn}: {order_details.get('error')}")
                return order_details, status_code

            buyer_user_id = order_details.get("data", {}).get("buyer_user", {}).get("user_id")
            order_id = order_details.get("data", {}).get("order_id")

            if not buyer_user_id or not order_id:
                logger.error(f"Could not extract buyer_user_id or order_id from order details for {order_sn}")
                return {"error": "Failed to get buyer or order ID from order details"}, 500

            # Get conversation info using buyer_user_id
            conversation_info, error = self._get_conversation_by_user_id(buyer_user_id)
            if error:
                logger.error(f"Failed to get conversation info for buyer {buyer_user_id}: {error}")
                return error, 500

            conversation_id = conversation_info.get("id")
            to_id = conversation_info.get("to_id")

            if not conversation_id or not to_id:
                logger.error(f"Could not get conversation_id or to_id for buyer {buyer_user_id}")
                return {"error": "Failed to get conversation info"}, 500

            # Construct the message payload
            message_payload = {
                "request_id": generate_request_id(),
                "to_id": to_id,
                "type": "order",
                "content": {
                    "order_id": order_id,
                    "shop_id": self.config.shop_id,
                    "uid": str(uuid.uuid4())
                },
                "shop_id": self.config.shop_id,
                "chat_send_option": {
                    "force_send_cancel_order_warning": False,
                    "comply_cancel_order_warning": False
                },
                "entry_point": "direct_chat_entry_point",
                "choice_info": {
                    "real_shop_id": None
                },
                "biz_id": 0,
                "conversation_id": conversation_id,
                "re_policy": {
                    "dfp_access_f": self.session.credential_manager.get_re_policy()
                }
            }

            # Send the message (no query parameters needed - authentication handled via headers)
            chat_response = self.session.post(
                self.config.urls["chat_message"],
                json=message_payload
            )

            if chat_response.status_code != 200:
                logger.error(f"Failed to send order card message: {chat_response.text}")
                return {"error": f"Failed to send order card message: {chat_response.text}"}, 500

            return chat_response.json(), 200
        except Exception as e:
            logger.exception(f"Exception in send_order_card_message: {e}")
            return {"error": f"Exception in send_order_card_message: {str(e)}"}, 500

    def send_order_message(self, order_sn: str) -> Tuple[Dict[str, Any], int]:
        """
        Send an order message to a user.

        Args:
            order_sn: Order number

        Returns:
            Tuple with response data and HTTP status code
        """
        # Print debug info

        try:
            # First, search for the order to get the order_id and buyer info
            try:
                from .orders import OrderService
            except ImportError:
                try:
                    from services.orders import OrderService
                except ImportError:
                    try:
                        import sys
                        import os
                        current_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
                        if current_dir not in sys.path:
                            sys.path.insert(0, current_dir)
                        from .orders import OrderService
                    except ImportError:
                        raise ImportError("Could not import OrderService")
            order_service = OrderService(self.session, self.config)

            order_data = order_service.search_order(order_sn)

            if not order_data.get('data', {}).get('card_list', []):
                return {"error": f"Order '{order_sn}' not found"}, 404

            # Extract order_id and buyer_user_id from the order data
            order = order_data['data']['card_list'][0]
            order_id = None
            buyer_user_id = None

            if 'package_level_order_card' in order:
                order_id = order['package_level_order_card']['order_ext_info'].get('order_id')
                buyer_user_id = order['package_level_order_card']['order_ext_info'].get('buyer_user_id')
            elif 'order_card' in order:
                order_id = order['order_card']['order_ext_info'].get('order_id')
                buyer_user_id = order['order_card']['order_ext_info'].get('buyer_user_id')

            if not order_id:
                return {"error": "Failed to extract order_id from order data"}, 500

            if not buyer_user_id:
                return {"error": "Failed to extract buyer_user_id from order data"}, 500


            # Get conversation info using the buyer_user_id directly
            conversation_info, error = self._get_conversation_by_user_id(buyer_user_id)
            if error:
                return error, 500

            # Debug conversation info

            # Extract conversation_id and to_id from the response
            conversation_id = conversation_info.get('id')
            to_id = conversation_info.get('to_id')

            if not conversation_id:
                return {"error": "Failed to extract conversation_id"}, 500

            if not to_id:
                return {"error": "Failed to extract to_id"}, 500


            # Generate a UUID for this message
            message_uuid = str(uuid.uuid4())

            # Prepare message payload to match the browser request
            message_payload = {
                "request_id": generate_request_id(),
                "to_id": to_id,
                "type": "text",  # Changed from "order" to "text" to match browser request
                "content": {
                    "text": f"Order: {order_sn}",  # Use text message instead of order message
                    "uid": message_uuid
                },
                "shop_id": self.config.shop_id,
                "chat_send_option": {
                    "force_send_cancel_order_warning": False,
                    "comply_cancel_order_warning": False
                },
                "entry_point": "direct_chat_entry_point",
                "choice_info": {
                    "real_shop_id": None
                },
                "biz_id": 0,  # Added from browser request
                "conversation_id": conversation_id,
                "re_policy": {
                    "dfp_access_f": "DxcBAAAABAAAAIAAAAX56VuH7gA9MBFXtSxFZALN2JACq8fwfq....."  # Use the value from browser request
                }
            }


            # Send the message (no query parameters needed - authentication handled via headers)
            chat_response = self.session.post(
                self.config.urls["chat_message"],
                json=message_payload
            )

            if chat_response.status_code != 200:
                return {"error": f"Failed to send order message: {chat_response.text}"}, 500

            return chat_response.json(), 200

        except Exception as e:
            return {"error": f"Exception in send_order_message: {str(e)}"}, 500

    def get_conversation_info_by_username(self, username: str) -> Tuple[Dict[str, Any], Optional[Dict[str, Any]]]:
        """
        Get conversation information by username.

        Args:
            username: Username to get conversation info for

        Returns:
            Tuple with conversation info and error (if any)
        """
        if not username:
            return None, {"error": "username parameter is required"}

        # Check if username is in cache first (most efficient)
        if self.config.cache["enabled"] and self.config.cache["username_to_conversation_id"]["enabled"]:
            cached_conversation = self.cache_manager.username_to_conversation_id.get(username.lower())
            if cached_conversation:
                logger.debug(f"Found cached conversation info for username: {username}")
                return cached_conversation, None

        # First, try to find the user in recent conversations (faster than search API)
        logger.debug(f"Looking for user '{username}' in recent conversations")
        try:
            recent_conversations, status_code = self.get_recent_conversations()
            if status_code == 200:
                # Check if we have conversations in the response
                conversations = []
                if 'conversations' in recent_conversations:
                    conversations = recent_conversations['conversations']
                elif isinstance(recent_conversations, list):
                    conversations = recent_conversations

                # Look for the user in the conversations
                for conv in conversations:
                    if conv.get('to_name', '').lower() == username.lower():
                        user_id = conv.get('to_id')
                        logger.debug(f"Found user '{username}' with ID {user_id} in recent conversations")
                        # Skip the search and go directly to getting conversation info
                        conversation_info, error = self._get_conversation_by_user_id(user_id)

                        # Cache the result if successful
                        if conversation_info and not error and self.config.cache["enabled"] and self.config.cache["username_to_conversation_id"]["enabled"]:
                            logger.debug(f"Caching conversation info for username: {username}")
                            self.cache_manager.username_to_conversation_id.set(username.lower(), conversation_info)

                        return conversation_info, error
        except Exception as e:
            logger.debug(f"Error searching recent conversations: {str(e)}")
            # Continue with normal search if there's an error

        logger.info(f"User '{username}' not found in recent conversations, using search API")

        # Prepare the params for the conversation search request using the new combined search API
        # Match the exact parameters from the working request
        params = {
            "per_page": 20,
            "keyword": username,
            "type": 3,
            "biz_id": 0,
            "_uid": f"0-{self.config.shop_id}",
            "_v": "8.8.9",  # Updated API version to match working request
            "csrf_token": self.session.credential_manager.get_csrf_token(),
            "SPC_CDS_CHAT": self.session.credential_manager.get_spc_cds_chat(),
            "x-shop-region": self.config.region_id,
            "_api_source": "webchat"
        }

        # Send the GET request to search for conversation info
        conversation_response = self.session.get(
            self.config.urls["conversation_search"],
            params=params
        )

        search_time = time.monotonic() - search_start
            timing_logger.info(f"  → Combined search API took {search_time:.3f}s")
            
            if conversation_response.status_code != 200:
            return None, {"error": "Failed to retrieve conversation info"}

        # Extract user_id from the search results using the new combined search API format
        search_results = conversation_response.json()
        user_id = None

        # Check the new conversation_search_result structure
        if 'conversation_search_result' in search_results and 'conversations' in search_results['conversation_search_result']:
            conversations = search_results['conversation_search_result']['conversations']

            # Look for a conversation with the matching username
            for conversation in conversations:
                # Check both to_name and buyer_name fields for the username
                to_name = conversation.get('to_name', '').lower()
                buyer_name = conversation.get('buyer_name', '').lower()

                if to_name == username.lower() or buyer_name == username.lower():
                    user_id = conversation.get('to_id') or conversation.get('buyer_id')
                    break

        # If we still don't have a user_id, try the order search results
        if not user_id and 'order_search_result' in search_results and 'orders' in search_results['order_search_result']:
            orders = search_results['order_search_result']['orders']

            for order in orders:
                # Check if this order has buyer information matching the username
                if order.get('buyer_name', '').lower() == username.lower():
                    user_id = order.get('buyer_id')
                    break

        # If we still don't have a user_id, return error
        if not user_id:
            # Fallback: Search in to_ship orders for new users who haven't chatted before
            logger.info(f"User '{username}' not found in combined search, trying order-based fallback")
            return self._get_conversation_info_from_orders(username)

        # Prepare the payload and params for the conversation request
        payload = {
            "user_id": user_id,
            "shop_id": self.config.shop_id
        }

        params = {
            "_uid": f"0-{self.config.shop_id}",
            "_v": "8.8.9",  # Updated API version to match search request
            "csrf_token": self.session.credential_manager.get_csrf_token(),
            "SPC_CDS_CHAT": self.session.credential_manager.get_spc_cds_chat(),
            "x-shop-region": self.config.region_id,
            "_api_source": "sc"
        }

        # Send the POST request to get conversation info
        conversation_response = self.session.post(
            self.config.urls["conversation"],
            params=params,
            json=payload
        )

        search_time = time.monotonic() - search_start
            timing_logger.info(f"  → Combined search API took {search_time:.3f}s")
            
            if conversation_response.status_code != 200:
            return None, {"error": "Failed to retrieve conversation info"}

        # Get the response data
        conversation_info = conversation_response.json()

        # Cache the result if successful
        if conversation_info and self.config.cache["enabled"] and self.config.cache["username_to_conversation_id"]["enabled"]:
            logger.debug(f"Caching conversation info for username: {username}")
            self.cache_manager.username_to_conversation_id.set(username.lower(), conversation_info)

        return conversation_info, None

    def get_conversation_info_by_ordersn(self, order_sn: str) -> Tuple[Dict[str, Any], Optional[Dict[str, Any]]]:
        """
        Get conversation information by order number.

        Args:
            order_sn: Order number to get conversation info for

        Returns:
            Tuple with conversation info and error (if any)
        """
        logger.debug(f"Getting conversation info for order: {order_sn}")

        if not order_sn:
            return None, {"error": "order_sn parameter is required"}

        # First, search for the order to get the buyer user ID
        from services.orders import OrderService
        order_service = OrderService(self.session, self.config)

        try:
            order_data = order_service.search_order(order_sn)

            if 'data' in order_data:
                if 'card_list' in order_data['data']:
                    pass  # Order data is valid, continue processing
            
            if not order_data.get('data', {}).get('card_list', []):
                return None, {"error": f"Order '{order_sn}' not found"}

        except Exception as e:
            return None, {"error": f"Failed to search for order: {str(e)}"}

        # Extract buyer user ID from the order data
        order = order_data['data']['card_list'][0]
        user_id = None
        order_id = None


        if 'package_level_order_card' in order:
            if 'order_ext_info' in order['package_level_order_card']:
                user_id = order['package_level_order_card']['order_ext_info'].get('buyer_user_id')
                order_id = order['package_level_order_card']['order_ext_info'].get('order_id')
            else:
                pass  # No order_ext_info available
        elif 'order_card' in order:
            if 'order_ext_info' in order['order_card']:
                user_id = order['order_card']['order_ext_info'].get('buyer_user_id')
                order_id = order['order_card']['order_ext_info'].get('order_id')
            else:
                pass  # No order_ext_info available
        else:
            pass  # No recognized order structure

        if not user_id:
            return None, {"error": "Failed to extract buyer user ID from order"}

        # Prepare the payload and params for the conversation request
        payload = {
            "user_id": user_id,
            "shop_id": self.config.shop_id
        }

        # Update API version to match what we see in the browser
        params = {
            "_uid": f"0-{self.config.shop_id}",
            "_v": "8.8.9",  # Updated from 8.5.6 to match browser request
            "csrf_token": self.session.credential_manager.get_csrf_token(),
            "SPC_CDS_CHAT": self.session.credential_manager.get_spc_cds_chat(),
            "x-shop-region": self.config.region_id,
            "_api_source": "sc"
        }

        logger.debug(f"Conversation request payload: {payload}")
        logger.debug(f"Conversation request params: {params}")
        logger.debug(f"Conversation URL: {self.config.urls['conversation']}")

        try:
            # Send the POST request to get conversation info
            conversation_response = self.session.post(
                self.config.urls["conversation"],
                params=params,
                json=payload
            )

            logger.debug(f"Conversation response status: {conversation_response.status_code}")

            search_time = time.monotonic() - search_start
            timing_logger.info(f"  → Combined search API took {search_time:.3f}s")
            
            if conversation_response.status_code != 200:
                return None, {"error": f"Failed to retrieve conversation info: {conversation_response.text}"}

            response_data = conversation_response.json()
            logger.debug(f"Conversation response structure: {list(response_data.keys()) if isinstance(response_data, dict) else 'Not a dict'}")

            return response_data, None

        except Exception as e:
            return None, {"error": f"Exception getting conversation info: {str(e)}"}

    def _get_conversation_by_user_id(self, user_id: int) -> Tuple[Dict[str, Any], Optional[Dict[str, Any]]]:
        """
        Get conversation information by user ID.

        Args:
            user_id: User ID to get conversation info for

        Returns:
            Tuple with conversation info and error (if any)
        """

        # Prepare the payload and params for the conversation request
        payload = {
            "user_id": user_id,
            "shop_id": self.config.shop_id
        }

        # Update API version to match what we see in the browser
        params = {
            "_uid": f"0-{self.config.shop_id}",
            "_v": "8.8.9",  # Updated from 8.5.6 to match browser request
            "csrf_token": self.session.credential_manager.get_csrf_token(),
            "SPC_CDS_CHAT": self.session.credential_manager.get_spc_cds_chat(),
            "x-shop-region": self.config.region_id,
            "_api_source": "sc"
        }

        logger.debug(f"Conversation request payload: {payload}")
        logger.debug(f"Conversation request params: {params}")
        logger.debug(f"Conversation URL: {self.config.urls['conversation']}")

        try:
            # Send the POST request to get conversation info
            conversation_response = self.session.post(
                self.config.urls["conversation"],
                params=params,
                json=payload
            )

            logger.debug(f"Conversation response status: {conversation_response.status_code}")

            search_time = time.monotonic() - search_start
            timing_logger.info(f"  → Combined search API took {search_time:.3f}s")
            
            if conversation_response.status_code != 200:
                return None, {"error": f"Failed to retrieve conversation info: {conversation_response.text}"}

            response_data = conversation_response.json()
            logger.debug(f"Conversation response structure: {list(response_data.keys()) if isinstance(response_data, dict) else 'Not a dict'}")

            return response_data, None

        except Exception as e:
            return None, {"error": f"Exception getting conversation info: {str(e)}"}

    def _get_conversation_info_from_orders(self, username: str) -> Tuple[Dict[str, Any], Optional[Dict[str, Any]]]:
        """
        Get conversation information by searching in to_ship orders and using redirection endpoint.
        This is used as a fallback when the user is not found in combined_search (e.g., new customers).

        Args:
            username: Username to search for

        Returns:
            Tuple with conversation info and error (if any)
        """
        if not username:
            return None, {"error": "Username is required"}

        logger.debug(f"Searching for user '{username}' in to_ship orders")

        try:
            # Import here to avoid circular imports
            from services.orders import OrderService
            order_service = OrderService(self.session, self.config)

            # Get to_ship orders
            to_ship_orders = order_service.get_to_ship_orders()
            
            if not to_ship_orders or 'data' not in to_ship_orders:
                logger.debug("No to_ship orders found")
                return None, {"error": "No orders found to search for user"}

            card_list = to_ship_orders['data'].get('card_list', [])
            logger.debug(f"Searching through {len(card_list)} orders for username: {username}")

            # Search for the user in orders
            buyer_user_id = None
            for card in card_list:
                order_card = card.get('order_card', {})
                card_header = order_card.get('card_header', {})
                buyer_info = card_header.get('buyer_info', {})
                order_ext_info = order_card.get('order_ext_info', {})
                
                # Check if this order belongs to the target username
                if buyer_info.get('username', '').lower() == username.lower():
                    buyer_user_id = order_ext_info.get('buyer_user_id')
                    logger.debug(f"Found user '{username}' with buyer_user_id: {buyer_user_id}")
                    break

            if not buyer_user_id:
                logger.debug(f"User '{username}' not found in to_ship orders")
                return None, {"error": f"User '{username}' not found in orders"}

            # Use conversation redirection endpoint to establish conversation
            return self._create_conversation_via_redirection(buyer_user_id, username)

        except Exception as e:
            logger.error(f"Exception searching orders for user '{username}': {str(e)}")
            return None, {"error": f"Exception searching orders: {str(e)}"}

    def _create_conversation_via_redirection(self, user_id: int, username: str) -> Tuple[Dict[str, Any], Optional[Dict[str, Any]]]:
        """
        Create/get conversation using the redirection endpoint for users not found in search.
        This is typically used for new customers who haven't chatted before.

        Args:
            user_id: User ID to create conversation with
            username: Username for logging purposes

        Returns:
            Tuple with conversation info and error (if any)
        """
        logger.debug(f"Creating conversation via redirection for user_id: {user_id}, username: {username}")

        # Prepare the payload for conversation redirection
        payload = {
            "user_id": user_id,
            "shop_id": self.config.shop_id,
            "biz_id": 0,
            "on_message_received": True,
            "entry_point": "direct_chat_entry_point"
        }

        # Prepare params for the redirection request
        params = {
            "_uid": f"0-{self.config.shop_id}",
            "_v": "8.8.9",
            "csrf_token": self.session.credential_manager.get_csrf_token(),
            "SPC_CDS_CHAT": self.session.credential_manager.get_spc_cds_chat(),
            "x-shop-region": self.config.region_id,
            "_api_source": "sc"
        }

        logger.debug(f"Redirection request payload: {payload}")
        logger.debug(f"Redirection request params: {params}")

        try:
            # Send the POST request to the conversation redirection endpoint
            response = self.session.post(
                self.config.urls["conversation"],
                params=params,
                json=payload
            )

            logger.debug(f"Redirection response status: {response.status_code}")

            if response.status_code != 200:
                logger.error(f"Redirection API error: {response.text}")
                return None, {"error": f"Failed to create conversation via redirection: HTTP {response.status_code}"}

            conversation_data = response.json()
            logger.debug(f"Redirection response: {conversation_data}")

            # Transform the response to match expected format
            conversation_info = {
                "id": conversation_data.get("id"),
                "conversation_id": conversation_data.get("id"),
                "to_id": conversation_data.get("to_id"),
                "to_name": conversation_data.get("to_name"),
                "to_avatar": conversation_data.get("to_avatar"),
                "to_avatar_hash": conversation_data.get("to_avatar_hash"),
                "shop_id": conversation_data.get("shop_id"),
                "shop_name": None,  # Not provided in redirection response
                "shop_region": self.config.region_id,
                "buyer_id": conversation_data.get("to_id"),
                "buyer_name": conversation_data.get("to_name")
            }

            logger.debug(f"Created conversation for user '{username}': {conversation_info}")
            return conversation_info, None

        except Exception as e:
            logger.error(f"Exception creating conversation via redirection for user '{username}': {str(e)}")
            return None, {"error": f"Exception creating conversation: {str(e)}"}

    def get_recent_conversations(self, unread_only: bool = False) -> Tuple[Dict[str, Any], int]:
        """
        Get recent conversations.

        Args:
            unread_only: Whether to only retrieve unread conversations

        Returns:
            Tuple with recent conversations data and HTTP status code
        """
        # Get CSRF token and SPC_CDS_CHAT
        csrf_token = self.session.credential_manager.get_csrf_token()
        spc_cds_chat = self.session.credential_manager.get_spc_cds_chat()

        # Check if required tokens are available
        if not csrf_token or not spc_cds_chat:
            return {"error": "Missing required authentication tokens. Please update your credentials."}, 401

        params = {
            "direction": "older",
            "_s": "1",
            "biz_id": 0,
            "_uid": f"0-{self.config.shop_id}",
            "_v": "8.8.4",
            "on_message_received": False,
            "csrf_token": csrf_token,
            "SPC_CDS_CHAT": spc_cds_chat,
            "x-shop-region": self.config.region_id
        }

        # Add unread_only parameter if True
        if unread_only:
            params["unread_only"] = "true"


        response = self.session.get(
            self.config.urls["recent_conversations"],
            params=params
        )

        if response.status_code != 200:
            return {"error": "Failed to retrieve recent conversations"}, 500

        # Properly structure the response as a dictionary
        # The Shopee API returns a list of conversations, so we wrap it in a dictionary
        response_data = response.json()
        if isinstance(response_data, list):
            return {"conversations": response_data}, 200
        else:
            # If it's already a dictionary, return it as is
            return response_data, 200

    def get_recent_latest_messages(self) -> List[Dict[str, Any]]:
        """
        Get recent latest messages from conversations.

        Returns:
            List of recent messages
        """
        try:
            conversations_response, status_code = self.get_recent_conversations()

            if status_code != 200:
                return []

            messages = []

            # Check if conversations are in the response
            if 'conversations' in conversations_response:
                # Make sure conversations is a list before iterating
                conversations = conversations_response['conversations']
                if not isinstance(conversations, list):
                    return []

                for conversation in conversations:
                    if not isinstance(conversation, dict):
                        continue

                    # Extract message data based on the structure
                    if 'latest_message' in conversation:
                        try:
                            message = {
                                'conversation_id': conversation.get('conversation_id'),
                                'to_id': conversation.get('to_id'),
                                'to_name': conversation.get('to_name'),
                                'to_avatar': conversation.get('to_avatar'),
                                'latest_message_content': conversation['latest_message'].get('content', {}).get('text', ''),
                                'latest_message_time': conversation['latest_message'].get('create_time'),
                                'unread_count': conversation.get('unread_count', 0)
                            }
                            messages.append(message)
                        except Exception as e:
                            logger.warning(f"Error processing conversation message: {e}")
                    # Alternative structure
                    elif 'latest_message_content' in conversation:
                        try:
                            message = {
                                'conversation_id': conversation.get('id'),
                                'to_id': conversation.get('to_id'),
                                'to_name': conversation.get('to_name'),
                                'to_avatar': conversation.get('to_avatar'),
                                'latest_message_content': conversation.get('latest_message_content', {}).get('text', ''),
                                'latest_message_time': conversation.get('latest_message_time'),
                                'unread_count': conversation.get('unread_count', 0)
                            }
                            messages.append(message)
                        except Exception as e:
                            logger.warning(f"Error processing alternative conversation structure: {e}")
            # For backward compatibility, also check the old structure
            elif 'data' in conversations_response and 'conversations' in conversations_response['data']:
                conversations = conversations_response['data']['conversations']
                if not isinstance(conversations, list):
                    return []

                for conversation in conversations:
                    if not isinstance(conversation, dict):
                        continue

                    if 'latest_message' in conversation:
                        try:
                            message = {
                                'conversation_id': conversation.get('conversation_id'),
                                'to_id': conversation.get('to_id'),
                                'to_name': conversation.get('to_name'),
                                'to_avatar': conversation.get('to_avatar'),
                                'latest_message_content': conversation['latest_message'].get('content', {}).get('text', ''),
                                'latest_message_time': conversation['latest_message'].get('create_time'),
                                'unread_count': conversation.get('unread_count', 0)
                            }
                            messages.append(message)
                        except Exception as e:
                            logger.warning(f"Error processing backward compatibility conversation: {e}")
            else:
                pass  # No conversations found

            return messages
        except Exception as e:
            return []

    def get_conversation_messages(self, conversation_id: str, offset: int = 0, limit: int = 20, direction: str = "older", force_refresh: bool = False) -> Tuple[Dict[str, Any], int, bool]:
        """
        Get messages from a specific conversation.

        Args:
            conversation_id: Conversation ID
            offset: Number of messages to skip
            limit: Maximum number of messages to return
            direction: Direction to retrieve messages ('older' or 'newer')
            force_refresh: Force refresh from Shopee API instead of using cache

        Returns:
            Tuple with conversation messages, HTTP status code, and a boolean indicating if the response was cached
        """
        # Check if we should use the cache
        # Import here to avoid circular imports
        try:
            from ..api import get_api
            api = get_api()
        except ImportError:
            # Fallback for when running outside of package context
            try:
                import sys
                import os
                # Add the parent directory to the path
                parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
                if parent_dir not in sys.path:
                    sys.path.insert(0, parent_dir)
                from api import get_api
                api = get_api()
            except ImportError:
                # If all imports fail, disable caching
                api = None

        # Check if WebSocket service is available and caching is enabled
        if not force_refresh and api is not None and hasattr(api, 'websocket_service') and api.websocket_service.is_caching_enabled():
            # Try to get messages from cache
            cached_data = api.websocket_service.get_cached_messages(conversation_id)
            if cached_data:

                # If we have cached data, return it
                # We might need to handle offset and limit here
                if offset == 0 and direction == "older":
                    # This is the most common case - getting the most recent messages
                    # Return the cached messages directly
                    # Ensure the cached data has the expected structure
                    if isinstance(cached_data, dict) and "messages" in cached_data:
                        return cached_data, 200, True
                    else:
                        # If cached data doesn't have the expected structure, wrap it
                        return {"messages": cached_data.get("messages", [])}, 200, True
                else:
                    # For other cases, we'll still use the API to ensure correct pagination
                    pass

        # If we get here, either caching is disabled, WebSocket is not connected,
        # force_refresh is True, or we need to use the API for pagination
        # Prepare parameters with proper error handling for encoding
        try:
            csrf_token = self.session.credential_manager.get_csrf_token()
            # Ensure csrf_token is a string before quoting
            if csrf_token is not None and not isinstance(csrf_token, str):
                csrf_token = str(csrf_token)

            # Handle potential encoding errors in quote function
            try:
                quoted_csrf_token = quote(csrf_token)
            except UnicodeEncodeError as e:
                return {"error": f"Encoding error in csrf_token: {str(e)}"}, 400, False

            params = {
                "shop_id": self.config.shop_id,
                "offset": str(offset),
                "limit": str(limit),
                "direction": direction,
                "biz_id": "0",
                "on_message_received": "true",
                "_uid": f"0-{self.config.shop_id}",
                "_v": "8.8.9",  # Updated API version to match other requests
                "csrf_token": quoted_csrf_token,
                "SPC_CDS_CHAT": self.session.credential_manager.get_spc_cds_chat(),
                "x-shop-region": self.config.region_id
            }
        except Exception as e:
            return {"error": f"Error preparing request: {str(e)}"}, 500, False


        try:
            # Ensure conversation_id is a string to prevent encoding issues
            conversation_id = str(conversation_id)

            # Use a try-except block specifically for URL encoding issues
            try:
                response = self.session.get(
                    f"{self.config.urls['conversation_messages']}/{conversation_id}/messages",
                    params=params
                )
            except UnicodeEncodeError as e:
                return {"error": f"Encoding error in URL: {str(e)}. Please check the conversation ID format."}, 400, False

            if response.status_code != 200:
                return {"error": "Failed to retrieve conversation messages"}, 500, False

            # If caching is enabled and this is a request for the most recent messages,
            # update the cache with the response
            response_data = response.json()

            # Ensure response data has the expected structure
            if not isinstance(response_data, dict):
                response_data = {"messages": response_data}
            elif "messages" not in response_data and isinstance(response_data, list):
                response_data = {"messages": response_data}

            if api is not None and hasattr(api, 'websocket_service') and api.websocket_service.is_caching_enabled() and offset == 0 and direction == "older":
                try:
                    # Update the cache with the new messages
                    api.websocket_service.cache_manager.conversation_messages.set(conversation_id, response_data)
                except Exception as e:
                    logger.warning(f"Error updating conversation cache: {e}")

            return response_data, 200, False  # False indicates this was not from cache
        except UnicodeEncodeError as e:
            return {"error": f"Encoding error: {str(e)}. Please check the input parameters."}, 400, False
        except Exception as e:
            return {"error": f"Exception in get_conversation_messages: {str(e)}"}, 500, False

    def get_conversation_messages_by_username(self, username: str, offset: int = 0, limit: int = 20, direction: str = "older", force_refresh: bool = False) -> Tuple[Dict[str, Any], int, bool]:
        """
        Get messages from a conversation with a specific user by username.

        This method first finds the conversation ID for the given username,
        then retrieves the messages for that conversation.

        Args:
            username: Username to get conversation messages for
            offset: Number of messages to skip
            limit: Maximum number of messages to return
            direction: Direction to retrieve messages (only 'older' is supported)
            force_refresh: Force refresh from Shopee API instead of using cache

        Returns:
            Tuple with conversation messages, HTTP status code, and a boolean indicating if the response was cached
        """
        # No need to check username type as it's already defined as str in the function signature


        # Get conversation info using the search endpoint
        conversation_info, error = self._get_conversation_info_from_search(username)
        if error:
            return {"error": error.get("error", "Failed to get conversation info")}, 500, False

        # Extract conversation_id from the search response
        conversation_id = conversation_info.get('id')
        if not conversation_id:
            return {"error": "Failed to get conversation info"}, 500, False

        return self.get_conversation_messages(conversation_id, offset, limit, direction, force_refresh)

    def set_conversation_unread(self, conversation_id: str) -> Tuple[Dict[str, Any], int]:
        """
        Mark a conversation as unread.

        Args:
            conversation_id: Conversation ID to mark as unread

        Returns:
            Tuple with response data and HTTP status code
        """

        params = {
            "request_id": int(time.time() * 1000),  # Current timestamp in milliseconds
            "_uid": f"0-{self.config.shop_id}",
            "_v": "8.6.2",
            "csrf_token": quote(self.session.credential_manager.get_csrf_token()),
            "SPC_CDS_CHAT": self.session.credential_manager.get_spc_cds_chat(),
            "x-shop-region": self.config.region_id
        }

        payload = {
            "biz_id": 0,
            "shop_id": self.config.shop_id
        }

        # Use the same base URL as other conversation endpoints
        url = f"{self.config.urls['recent_conversations']}/{conversation_id}/unread"

        try:
            response = self.session.put(url, params=params, json=payload)

            if response.status_code != 200:
                return {"error": f"Failed to mark conversation as unread: {response.text}"}, response.status_code

            return {"success": True, "message": "Conversation marked as unread"}, 200
        except Exception as e:
            return {"error": f"Exception setting conversation unread: {str(e)}"}, 500

    def set_conversation_unread_by_username(self, username: str) -> Tuple[Dict[str, Any], int]:
        """
        Mark a conversation as unread by username.

        Args:
            username: Username of the conversation to mark as unread

        Returns:
            Tuple with response data and HTTP status code
        """

        # First, get the conversation info to find the conversation ID
        conversation_info, error = self.get_conversation_info_by_username(username)

        if error:
            return {"error": f"Failed to find conversation for username '{username}': {error.get('error', 'Unknown error')}"}, 404

        if not conversation_info:
            return {"error": f"No conversation found for username '{username}'"}, 404

        # Extract conversation_id from the response
        # Try different paths in the response structure
        conversation_id = None

        # Path 1: Standard structure
        if 'data' in conversation_info and 'conversation_info' in conversation_info['data']:
            conversation_id = conversation_info['data']['conversation_info'].get('conversation_id')

        # Path 2: Alternative structure
        elif 'conversation_id' in conversation_info:
            conversation_id = conversation_info.get('conversation_id')

        # Path 3: Nested structure
        elif 'data' in conversation_info and 'conversation' in conversation_info['data']:
            conversation_id = conversation_info['data']['conversation'].get('conversation_id')

        # Path 4: ID structure
        elif 'data' in conversation_info and 'id' in conversation_info['data']:
            conversation_id = conversation_info['data'].get('id')

        # Path 5: Direct ID
        elif 'id' in conversation_info:
            conversation_id = conversation_info.get('id')

        if not conversation_id:
            return {"error": f"Failed to extract conversation ID for username '{username}'"}, 500

        # Now mark the conversation as unread
        return self.set_conversation_unread(conversation_id)

    def get_image_cache_stats(self) -> Dict[str, Any]:
        """
        Get image cache statistics.

        Returns:
            Dictionary with cache statistics
        """
        return self.image_cache.get_cache_stats()
    
    def clear_image_cache(self) -> Dict[str, Any]:
        """
        Clear all image cache entries.

        Returns:
            Dictionary with status information
        """
        cleared_count = self.image_cache.clear_cache()
        return {
            "success": True,
            "message": f"Cleared {cleared_count} image cache entries",
            "entries_cleared": cleared_count
        }
    
    def periodic_image_cache_cleanup(self) -> Dict[str, Any]:
        """
        Perform periodic cleanup of image cache.

        Returns:
            Dictionary with cleanup results
        """
        try:
            self.image_cache.periodic_cleanup()
            stats = self.image_cache.get_cache_stats()
            return {
                "success": True,
                "message": "Image cache cleanup completed",
                "current_stats": stats
            }
        except Exception as e:
            return {
                "success": False,
                "message": f"Image cache cleanup failed: {str(e)}",
                "error": str(e)
            }

    def clear_cache(self) -> Dict[str, Any]:
        """
        Clear all caches.

        Returns:
            Dictionary with status information
        """

        # Clear username to conversation ID cache
        username_cache_size = len(self.cache_manager.username_to_conversation_id.cache)
        self.cache_manager.username_to_conversation_id.clear()
        
        # Clear image cache
        image_cache_size = self.image_cache.clear_cache()

        return {
            "success": True,
            "message": "All caches cleared",
            "details": {
                "username_to_conversation_id_cleared": username_cache_size,
                "image_cache_cleared": image_cache_size
            }
        }
