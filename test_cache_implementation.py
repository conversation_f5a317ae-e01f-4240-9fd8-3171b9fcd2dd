#!/usr/bin/env python3
"""
Test script to verify the "forever" cache implementation
This will test both cold cache (first message) and warm cache (subsequent messages)
"""

import time
import json
import requests
import sys
import os

def test_cache_performance():
    """Test cache performance with multiple messages to the same user"""
    print("CACHE IMPLEMENTATION TEST")
    print("Username: me0tn_14qo")
    print("=" * 60)
    
    # Use the remote API
    base_url = 'https://shop.api.limjianhui.com'
    api_key = 'MTYB_OFFICIAL'
    
    url = f"{base_url}/chat/send_message"
    headers = {
        'Content-Type': 'application/json',
        'X-API-Key': api_key
    }
    
    # Test messages
    test_messages = [
        "Cache test #1 - Cold cache (first lookup)",
        "Cache test #2 - Warm cache (should be faster)",
        "Cache test #3 - Warm cache (should be faster)",
    ]
    
    times = []
    
    for i, message in enumerate(test_messages, 1):
        print(f"\n--- Test {i}/3: {message} ---")
        
        payload = {
            "username": "me0tn_14qo",
            "text": f"{message} at {time.strftime('%H:%M:%S')}",
            "force_send_cancel_order_warning": False,
            "comply_cancel_order_warning": False
        }
        
        print(f"Sending: {payload['text']}")
        
        # Measure time
        start_time = time.monotonic()
        
        try:
            response = requests.post(url, headers=headers, json=payload, timeout=60)
            end_time = time.monotonic()
            
            total_time = end_time - start_time
            times.append(total_time)
            
            print(f"⏱️  Time: {total_time:.3f} seconds")
            print(f"📊 Status: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                print("✅ Success!")
                
                # Extract key info
                if 'data' in result:
                    data = result['data']
                    print(f"   Message ID: {data.get('id')}")
                    print(f"   Conversation ID: {data.get('conversation_id')}")
                    
            else:
                print(f"❌ Error: {response.text}")
                
        except Exception as e:
            end_time = time.monotonic()
            total_time = end_time - start_time
            times.append(total_time)
            print(f"❌ Failed after {total_time:.3f}s: {e}")
        
        # Wait between tests
        if i < len(test_messages):
            print("   Waiting 3 seconds...")
            time.sleep(3)
    
    return times

def analyze_cache_performance(times):
    """Analyze the performance results"""
    print("\n" + "=" * 60)
    print("CACHE PERFORMANCE ANALYSIS")
    print("=" * 60)
    
    if len(times) < 3:
        print("❌ Not enough test results for analysis")
        return
    
    cold_cache_time = times[0]  # First message (cache miss)
    warm_cache_times = times[1:]  # Subsequent messages (cache hits)
    avg_warm_time = sum(warm_cache_times) / len(warm_cache_times)
    
    print(f"Cold cache (1st message): {cold_cache_time:.3f}s")
    print(f"Warm cache (avg):         {avg_warm_time:.3f}s")
    
    if avg_warm_time < cold_cache_time:
        improvement = ((cold_cache_time - avg_warm_time) / cold_cache_time) * 100
        time_saved = cold_cache_time - avg_warm_time
        
        print(f"\n🚀 CACHE WORKING!")
        print(f"   Improvement: {improvement:.1f}% faster")
        print(f"   Time saved:  {time_saved:.3f}s per message")
        print(f"   Expected:    ~60% improvement (target: ~2.3s)")
        
        if improvement >= 50:
            print("   ✅ Cache is working as expected!")
        elif improvement >= 20:
            print("   ⚠️  Cache is working but less improvement than expected")
        else:
            print("   ❓ Cache may not be working optimally")
            
    else:
        print(f"\n❌ CACHE NOT WORKING")
        print("   Warm cache should be faster than cold cache")
        print("   Check cache configuration and implementation")
    
    print(f"\nDetailed times:")
    for i, t in enumerate(times, 1):
        cache_status = "COLD" if i == 1 else "WARM"
        print(f"   Test {i}: {t:.3f}s ({cache_status})")

def check_cache_configuration():
    """Check if cache configuration is correct"""
    print("\n" + "=" * 60)
    print("CACHE CONFIGURATION CHECK")
    print("=" * 60)
    
    config_file = "ShopeeAPI/config.json"
    
    try:
        with open(config_file, 'r') as f:
            config = json.load(f)
        
        cache_config = config.get('CACHE', {})
        username_cache = cache_config.get('USERNAME_TO_CONVERSATION_ID', {})
        
        print("Current cache configuration:")
        print(f"   Cache enabled: {cache_config.get('ENABLED', False)}")
        print(f"   Username cache enabled: {username_cache.get('ENABLED', False)}")
        print(f"   Expiry seconds: {username_cache.get('EXPIRY_SECONDS', 'Not set')}")
        print(f"   Max size: {username_cache.get('MAX_SIZE', 'Not set')}")
        
        # Check if it's configured for "forever"
        expiry = username_cache.get('EXPIRY_SECONDS', 0)
        if expiry >= 315360000:  # 10 years
            print("   ✅ Configured for 'forever' cache (10+ years)")
        elif expiry >= 86400:  # 1 day
            print(f"   ⚠️  Cache expiry is {expiry/86400:.1f} days (not 'forever')")
        else:
            print(f"   ❌ Cache expiry is too short: {expiry} seconds")
            
        max_size = username_cache.get('MAX_SIZE', 0)
        if max_size >= 10000:
            print("   ✅ Large cache size configured")
        else:
            print(f"   ⚠️  Small cache size: {max_size}")
            
    except Exception as e:
        print(f"❌ Error reading config: {e}")

def main():
    """Run the cache implementation test"""
    # Check configuration first
    check_cache_configuration()
    
    # Run performance tests
    times = test_cache_performance()
    
    # Analyze results
    analyze_cache_performance(times)
    
    print("\n" + "=" * 60)
    print("IMPLEMENTATION STATUS")
    print("=" * 60)
    print("✅ Cache configuration updated (10 years expiry)")
    print("✅ send_chat_message modified to use cache-aware lookup")
    print("✅ send_image_message modified to use cache-aware lookup")
    print("✅ Performance test completed")
    
    print("\nNext steps:")
    print("• Monitor cache hit rates in production")
    print("• Check cache file: ShopeeAPI/cache/username_conversation_cache.json")
    print("• Clear cache if needed: DELETE the cache file")

if __name__ == "__main__":
    main()
