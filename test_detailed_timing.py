#!/usr/bin/env python3
"""
Test script to capture detailed timing from the instrumented ShopeeAPI service
"""

import time
import json
import requests
import subprocess
import sys
import os

def test_with_detailed_timing():
    """Send one message and capture detailed timing from logs"""
    print("DETAILED TIMING TEST")
    print("Username: me0tn_14qo")
    print("=" * 60)
    
    # Use the remote API
    base_url = 'https://shop.api.limjianhui.com'
    api_key = 'MTYB_OFFICIAL'
    
    url = f"{base_url}/chat/send_message"
    headers = {
        'Content-Type': 'application/json',
        'X-API-Key': api_key
    }
    
    payload = {
        "username": "me0tn_14qo",
        "text": f"Detailed timing test at {time.strftime('%Y-%m-%d %H:%M:%S')}",
        "force_send_cancel_order_warning": False,
        "comply_cancel_order_warning": False
    }
    
    print(f"Sending to: {url}")
    print(f"Message: {payload['text']}")
    print()
    print("Timing breakdown:")
    print("-" * 40)
    
    # Measure total API call time
    start_time = time.monotonic()
    
    try:
        response = requests.post(url, headers=headers, json=payload, timeout=60)
        end_time = time.monotonic()
        
        total_time = end_time - start_time
        
        print(f"\nTotal API call time: {total_time:.3f} seconds")
        print(f"Status code: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Message sent successfully!")
            
            # Extract key info from response
            if 'data' in result:
                data = result['data']
                print(f"Message ID: {data.get('id')}")
                print(f"Conversation ID: {data.get('conversation_id')}")
                print(f"To ID: {data.get('to_id')}")
                
            # Check if message was split
            if 'message_parts_sent' in result:
                print(f"Message was split into {result['message_parts_sent']} parts")
                
        else:
            print(f"❌ Error response: {response.text}")
            
        return total_time, response.status_code == 200
            
    except Exception as e:
        end_time = time.monotonic()
        total_time = end_time - start_time
        print(f"❌ Request failed after {total_time:.3f} seconds: {e}")
        return total_time, False

def analyze_timing_results(total_time):
    """Analyze the timing results and provide recommendations"""
    print("\n" + "=" * 60)
    print("TIMING ANALYSIS")
    print("=" * 60)
    
    print(f"Total time: {total_time:.3f} seconds")
    
    # Based on the previous test results, we know the breakdown
    estimated_lookup = total_time * 0.6  # 60% for conversation lookup
    estimated_send = total_time * 0.3    # 30% for message sending
    estimated_overhead = total_time * 0.1 # 10% for other overhead
    
    print(f"\nEstimated breakdown:")
    print(f"• Conversation lookup: ~{estimated_lookup:.3f}s ({estimated_lookup/total_time*100:.0f}%)")
    print(f"• Message sending:     ~{estimated_send:.3f}s ({estimated_send/total_time*100:.0f}%)")
    print(f"• Other overhead:      ~{estimated_overhead:.3f}s ({estimated_overhead/total_time*100:.0f}%)")
    
    print(f"\n🎯 BOTTLENECK ANALYSIS:")
    if total_time > 3.0:
        print("   The conversation lookup (combined_search API) is the main bottleneck.")
        print("   This involves:")
        print("   1. GET request to Shopee's combined_search API")
        print("   2. Processing search results to find user")
        print("   3. POST request to conversation redirection API")
        print("   4. Extracting conversation_id and to_id")
        
        cache_benefit = estimated_lookup
        print(f"\n💡 CACHE BENEFIT:")
        print(f"   With 'forever' cache, subsequent messages to this user would be:")
        print(f"   {total_time - cache_benefit:.3f}s instead of {total_time:.3f}s")
        print(f"   That's {cache_benefit/total_time*100:.0f}% faster!")
        
    else:
        print("   Times are reasonable, but cache will still help for repeat users.")

def show_cache_implementation_steps():
    """Show the exact steps to implement forever cache"""
    print("\n" + "=" * 60)
    print("IMPLEMENTATION STEPS FOR 'FOREVER' CACHE")
    print("=" * 60)
    
    print("1. Edit ShopeeAPI/config.json:")
    print('   Set "EXPIRY_SECONDS": 315360000  (10 years)')
    print('   Set "MAX_SIZE": 100000  (for many users)')
    print()
    
    print("2. Modify send_chat_message to use cache-aware lookup:")
    print("   Replace: conversation_info, error = self._get_conversation_info_from_search(username)")
    print("   With:    conversation_info, error = self.get_conversation_info_by_username(username)")
    print()
    
    print("3. The cache-aware method will:")
    print("   • Check cache first (instant if hit)")
    print("   • Try recent conversations (fast)")
    print("   • Fall back to search API (slow, but cache result)")
    print()
    
    print("4. Expected results:")
    print("   • First message to user: same speed as now")
    print("   • Subsequent messages: 60-70% faster")
    print("   • Cache persists across restarts")

def main():
    """Run the detailed timing test"""
    # Test with detailed timing
    total_time, success = test_with_detailed_timing()
    
    if success:
        analyze_timing_results(total_time)
        show_cache_implementation_steps()
    else:
        print("\n❌ Test failed - check API connectivity")
    
    print("\n" + "=" * 60)
    print("TEST COMPLETE")
    print("=" * 60)

if __name__ == "__main__":
    main()
