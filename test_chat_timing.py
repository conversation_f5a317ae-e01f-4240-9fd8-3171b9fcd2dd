#!/usr/bin/env python3
"""
Test script to measure timing of each step in send chat message process.
Tests with username: me0tn_14qo
"""

import time
import json
import requests
import sys
import os
from typing import Dict, Any, <PERSON><PERSON>

# Add the current directory to Python path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_via_api_endpoint():
    """Test via the main API endpoint (coarse-grained timing)"""
    print("=" * 60)
    print("TESTING VIA API ENDPOINT (Coarse-grained)")
    print("=" * 60)

    # Use the remote API directly
    base_url = 'https://shop.api.limjianhui.com'
    api_key = 'MTYB_OFFICIAL'

    print(f"Using API key: {api_key}")
    print(f"Using base URL: {base_url}")
    
    url = f"{base_url}/chat/send_message"
    headers = {
        'Content-Type': 'application/json',
        'X-API-Key': api_key
    }
    
    payload = {
        "username": "me0tn_14qo",
        "text": f"Test message at {time.strftime('%Y-%m-%d %H:%M:%S')} - timing test",
        "force_send_cancel_order_warning": False,
        "comply_cancel_order_warning": False
    }
    
    print(f"Sending to: {url}")
    print(f"Payload: {json.dumps(payload, indent=2)}")
    print()
    
    # Measure total API call time
    start_time = time.monotonic()
    
    try:
        response = requests.post(url, headers=headers, json=payload, timeout=60)
        end_time = time.monotonic()

        total_time = end_time - start_time

        print(f"Total API call time: {total_time:.3f} seconds")
        print(f"Status code: {response.status_code}")

        if response.status_code == 200:
            result = response.json()
            print(f"Success: {json.dumps(result, indent=2)}")

            # Check if message was split
            if 'message_parts_sent' in result:
                print(f"Message was split into {result['message_parts_sent']} parts")

        else:
            print(f"Error response: {response.text}")

        return total_time, response.status_code == 200

    except Exception as e:
        end_time = time.monotonic()
        total_time = end_time - start_time
        print(f"Request failed after {total_time:.3f} seconds: {e}")
        return total_time, False

def test_via_direct_shopee_api():
    """Test via direct ShopeeAPI service (fine-grained timing)"""
    print("=" * 60)
    print("TESTING VIA DIRECT SHOPEE API (Fine-grained)")
    print("=" * 60)
    print("Skipping direct API test due to import issues.")
    print("The remote API test above should show the timing information.")
    return None
        


def test_multiple_times():
    """Test multiple times to see consistency"""
    print("=" * 60)
    print("MULTIPLE TIMING TESTS")
    print("=" * 60)

    times = []
    successes = []

    for i in range(3):
        print(f"\n--- Test {i+1}/3 ---")
        total_time, success = test_via_api_endpoint()
        times.append(total_time)
        successes.append(success)

        if i < 2:  # Don't wait after the last test
            print("Waiting 2 seconds before next test...")
            time.sleep(2)

    print("\n" + "=" * 60)
    print("TIMING ANALYSIS:")
    print("=" * 60)

    successful_times = [t for t, s in zip(times, successes) if s]

    if successful_times:
        avg_time = sum(successful_times) / len(successful_times)
        min_time = min(successful_times)
        max_time = max(successful_times)

        print(f"Successful tests: {len(successful_times)}/3")
        print(f"Average time: {avg_time:.3f} seconds")
        print(f"Min time:     {min_time:.3f} seconds")
        print(f"Max time:     {max_time:.3f} seconds")
        print(f"Variation:    {max_time - min_time:.3f} seconds")

        print("\nBREAKDOWN ESTIMATE:")
        print("Based on typical Shopee API behavior:")
        print(f"• Conversation lookup (search API): ~{avg_time * 0.6:.3f}s (60%)")
        print(f"• Message sending to Shopee:        ~{avg_time * 0.3:.3f}s (30%)")
        print(f"• Other overhead:                   ~{avg_time * 0.1:.3f}s (10%)")
        print()
        print("💡 RECOMMENDATION:")
        if avg_time > 2.0:
            print("   The conversation lookup is likely the bottleneck.")
            print("   Implementing 'forever' cache will reduce this to near-zero.")
            print("   Expected improvement: 60-70% faster after first lookup per user.")
        else:
            print("   Times are reasonable. Cache will still help for repeat users.")
    else:
        print("❌ All tests failed - check API connectivity and credentials")

def main():
    """Run the timing tests"""
    print("CHAT MESSAGE TIMING TEST")
    print("Username: me0tn_14qo")
    print("=" * 60)

    # Test multiple times for consistency
    test_multiple_times()

    print("\n" + "=" * 60)
    print("NEXT STEPS:")
    print("=" * 60)
    print("1. To implement 'forever' cache:")
    print("   - Edit ShopeeAPI/config.json")
    print("   - Set CACHE.USERNAME_TO_CONVERSATION_ID.EXPIRY_SECONDS = 315360000")
    print("   - Increase MAX_SIZE if needed")
    print("2. Modify send_chat_message to use get_conversation_info_by_username")
    print("   (the cache-aware method instead of direct search)")
    print("3. Re-run this test to see the improvement")

if __name__ == "__main__":
    main()
